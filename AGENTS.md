# Gelişmiş Vue 3 Uygulama Mimarisi

Bu doküman, Vue 3, TypeScript, VoltUI (PrimeVue) ve TailwindCSS teknolojileri kullanılarak geliştirilecek uygulamalar için önerilen modüler, ölçeklenebilir ve sürdürülebilir mimariyi tanımlar.

## 1. Temel Felsefe ve Prensipler

Bu mimari, aşağıdaki temel prensipler üzerine kurulmuştur:

*   **Modülerlik (Module Isolation)**: Her işlevsel birim, diğerlerinden tamamen izole edilmiş kendi kendine yeten bir modül olarak geliştirilir.
*   **Sorumlulukların Ayrılması (Separation of Concerns)**: Uygulamanın çekirdek mantığı (veri erişimi, loglama, bildirim) ile iş mantığı (modüller) kesin çizgilerle ayrılmıştır.
*   **CQRS (Command Query Responsibility Segregation)**: <PERSON><PERSON> (Query) ve veri yazma (Command) sorumlulukları, farklı veri kaynaklarını kullanabilecek şekilde ayrıştırılmıştır.
*   **Soyutlama Katmanları (Abstraction Layers)**: Modüller, çekirdek servislerle doğrudan konuşmaz. Bunun yerine, aracı Hook'lar aracılığıyla iletişim kurarak karmaşıklığı gizler ve tutarlı bir API sunar.
*   **Tek Sorumluluk Prensibi (Dosya Seviyesi)**: Her dosya sadece tek bir tanım (interface, class, function, type, enum vb.) içerir. Bu, kodun bulunabilirliğini artırır ve dosyaların şişmesini engeller. Dosya adları, içeriğini yansıtacak şekilde bir sonek almalıdır (örn: .interface.ts, .type.ts, .service.ts).

## 2. Genel Dizin Yapısı

```
.
├── public/
├── src/
│   ├── assets/
│   ├── components/             # Paylaşılan, atomik bileşenler (BaseButton, BaseInput vb.)
│   ├── core/
│   │   ├── data/
│   │   │   ├── config/
│   │   │   │   └── data.config.ts          # .env okuma, tip güvenli konfigürasyon
│   │   │   ├── providers/
│   │   │   │   ├── firebase/
│   │   │   │   │   ├── FirebaseCommand.service.ts
│   │   │   │   │   └── FirebaseQuery.service.ts
│   │   │   │   └── rest/
│   │   │   │       ├── RestApiCommand.service.ts
│   │   │   │       └── RestApiQuery.service.ts
│   │   │   ├── interfaces/
│   │   │   │   ├── ICommand.interface.ts
│   │   │   │   └── IQuery.interface.ts
│   │   │   ├── hooks/
│   │   │   │   ├── useCommandService.hook.ts
│   │   │   │   └── useQueryService.hook.ts
│   │   │   ├── service.factory.ts       # Servisleri üreten fabrika
│   │   │   └── index.ts                 # Sadece hook'ları dışa aktarır
│   │   ├── logger/
│   │   │   ├── hooks/
│   │   │   │   └── useLogger.hook.ts
│   │   │   ├── Logger.service.ts
│   │   │   └── index.ts
│   │   └── notification/
│   │       ├── hooks/
│   │       │   └── useNotification.hook.ts
│   │       ├── Notification.service.ts
│   │       └── index.ts
│   ├── modules/
│   │   └── todos/
│   │       ├── components/
│   │       │   ├── TodoItem.vue
│   │       │   └── index.ts
│   │       ├── router/
│   │       │   ├── todos.routes.ts
│   │       │   └── index.ts
│   │       ├── types/
│   │       │   ├── ITodo.interface.ts
│   │       │   ├── TodoStatus.enum.ts
│   │       │   └── index.ts
│   │       ├── views/
│   │       │   ├── TodoListView.vue
│   │       │   └── index.ts
│   │       └── index.ts                 # Modülün ana giriş noktası (kullanılmaz)
│   ├── router/
│   │   └── index.ts                     # Ana router
│   ├── App.vue
│   └── main.ts
├── .env
├── package.json
└── tsconfig.json
```

## 3. Katmanların Detaylı Açıklaması

### 3.1. Yapılandırma Katmanı (.env ve config)

Uygulamanın davranışını belirleyen tüm ortam değişkenleri burada yönetilir.

#### .env Dosyası:

```ini
# Okuma işlemleri için: 'firebase' veya 'rest'
VITE_QUERY_DATA_SOURCE=rest

# Yazma (CUD) işlemleri için: 'firebase' veya 'rest'
VITE_COMMAND_DATA_SOURCE=firebase

# Diğer Ayarlar
VITE_LOG_LEVEL=debug
VITE_FIREBASE_API_KEY=your_api_key
VITE_REST_API_BASE_URL=https://api.example.com/
```

#### data.config.ts:

Bu dosya, zod gibi bir kütüphane kullanarak .env değişkenlerini okur, doğrular ve tüm uygulama için tip güvenli bir konfigürasyon nesnesi sağlar.

### 3.2. Çekirdek Veri Katmanı (src/core/data)

Bu katman, veri kaynağı ile olan tüm iletişimi soyutlar.

#### service.factory.ts (Fabrika):

Mimarinin en kritik parçasıdır. data.config.ts dosyasından okuduğu yapılandırmaya göre doğru Query ve Command servislerinin birer örneğini oluşturur ve talep eden Hook'a döndürür.

```typescript
// src/core/data/service.factory.ts
import { config } from './config/data.config';
import { IQuery } from './interfaces/IQuery.interface';
import { ICommand } from './interfaces/ICommand.interface';
// ... Servis importları ...

export function getQueryService<T>(collection: string): IQuery<T> {
  switch (config.VITE_QUERY_DATA_SOURCE) {
    case 'firebase': return new FirebaseQueryService<T>(collection);
    case 'rest': return new RestApiService<T>(collection);
    default: throw new Error('Unsupported query data source');
  }
}

export function getCommandService<T>(collection: string): ICommand<T> {
  switch (config.VITE_COMMAND_DATA_SOURCE) {
    case 'firebase': return new FirebaseCommandService<T>(collection);
    case 'rest': return new RestApiCommandService<T>(collection);
    default: throw new Error('Unsupported command data source');
  }
}
```

#### Servisler (providers):

Her servis, IQuery veya ICommand arayüzlerinden birini uygular. Görevleri, ham CRUD işlemlerini gerçekleştirmektir. Kesinlikle try-catch bloğu içermezler; ham hatayı fırlatırlar.

### 3.3. Çekirdek Kancalar (Core Hooks)

Modüllerin çekirdek sistemle tek etkileşim noktasıdır.

#### useQueryService.hook.ts / useCommandService.hook.ts:

*   **Görevi**: Modülden gelen isteği alır.
*   `service.factory.ts`'i çağırarak uygun servisi alır.
*   Servisin metodunu bir try-catch bloğu içinde çalıştırır.
*   Başarı durumunda veriyi, loading, error gibi reaktif durumlarla birlikte modüle döndürür.
*   Hata durumunda, `useLogger` ile hatayı loglar ve `useNotification` ile kullanıcıya bir bildirim gösterir.

### 3.4. Modül Katmanı (src/modules)

Uygulamanın iş mantığının yaşadığı yerdir.

*   **İzolasyon**: Bir modül, başka bir modülün dosyasını ASLA import etmez.
*   **Klasör Yapısı**: Her dosya türü (components, views, types, router) kendi klasöründe yaşar.
*   **Barrel Dosyaları (index.ts)**: Her klasör, kendi içeriğini bir `index.ts` dosyası aracılığıyla dışa aktarır. Bu, import yollarını temizler ve modülün API'sini netleştirir.

#### Örnek src/modules/todos/types/index.ts:

```typescript
export * from './ITodo.interface';
export * from './TodoStatus.enum';
```

#### Örnek Bir Bileşenin import Yolu:

```typescript
// TodoListView.vue içinde
import { TodoItem } from '../components'; // ../components/index.ts'ten gelir
import type { ITodo } from '../types';    // ../types/index.ts'ten gelir
```

## 4. AGENTS.md (Mimari Ajanları)

Bu bölümde, mimari bileşenler "Ajanlar" olarak tanımlanarak sorumlulukları metaforik bir dille açıklanmıştır.

### Ajan 1: Modül (Module Agent)

*   **Görevi**: Belirli bir işlevselliği yönetir. Veriyi Core Hook'lardan talep eder ve ekranda gösterir.
*   **Kural**: Sadece Core Hook'ları ve src/components altındaki genel bileşenleri kullanabilir. Diğer modüllere ve Core Servis'lere erişemez.

### Ajan 2: Çekirdek Kanca (Core Hook Agent)

*   **Görevi**: Modül ile Çekirdek Servis arasında güvenli, akıllı bir köprüdür. Hata yönetimi, loglama ve bildirim gibi yan etkileri yönetir.
*   **Kural**: Service Factory aracılığıyla Çekirdek Servis'leri çağırır. Modül'ler hakkında hiçbir şey bilmez.

### Ajan 3: Çekirdek Servis (Core Service Agent)

*   **Görevi**: Tek bir işi yapar: Veritabanına sorgu atmak, konsola log basmak vb. Ham iş mantığını içerir.
*   **Kural**: Hata yakalamaz, fırlatır. Diğer Hook veya Servis'leri çağıramaz.

### Ajan 4: Fabrika (Factory Agent)

*   **Görevi**: Konfigürasyon Ajanı'ndan aldığı bilgiye göre, hangi Çekirdek Servis'in oluşturulacağına karar verir ve bir örneğini Çekirdek Kanca'ya teslim eder.
*   **Kural**: Sadece Servis'leri ve Konfigürasyon'u tanır.

### Ajan 5: Konfigürasyon (Configuration Agent)

*   **Görevi**: .env dosyasını okur, doğrular ve tip güvenli bir nesne olarak tüm uygulamanın kullanımına sunar.
*   **Kural**: Sadece dışa aktarım yapar, kimseyi tanımaz.